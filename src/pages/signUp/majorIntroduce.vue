<template>
  <view class="page-container">
    <!-- 滚动内容区域 -->
    <scroll-view
      scroll-y="true"
      class="content-scroll"
      :show-scrollbar="false"
      enhanced
    >
      <view class="content-container">
        <!-- 专业名称 -->
        <view class="info-section">
          <view class="section-title">专业名称</view>
          <view class="section-content">
            <view class="content-item">
              <view class="dot"></view>
              <text class="content-text">{{ majorIntroduce.majorName }}</text>
            </view>
          </view>
        </view>

        <!-- 学制 -->
        <view class="info-section">
          <view class="section-title">学制</view>
          <view class="section-content">
            <view class="content-item">
              <view class="dot"></view>
              <text class="content-text">{{
                majorIntroduce.educationalSystem
              }}</text>
            </view>
          </view>
        </view>

        <!-- 专业介绍 -->
        <view class="info-section">
          <view class="section-title">专业介绍</view>
          <view class="section-content">
            <view class="content-item">
              <view class="dot"></view>
              <text class="content-text long-text">{{
                majorIntroduce.majorIntroduction
              }}</text>
            </view>
          </view>
        </view>

        <!-- 核心课程 -->
        <view class="info-section">
          <view class="section-title">核心课程</view>
          <view class="section-content">
            <view class="content-item">
              <view class="dot"></view>
              <text class="content-text">{{
                majorIntroduce.coreCurriculum
              }}</text>
            </view>
          </view>
        </view>

        <!-- 就业方向 -->
        <view class="info-section">
          <view class="section-title">就业方向</view>
          <view class="section-content">
            <view class="content-item">
              <view class="dot"></view>
              <text class="content-text long-text">{{
                majorIntroduce.employmentDirection
              }}</text>
            </view>
          </view>
        </view>
        <!-- 学费 -->
        <view class="info-section">
          <view class="section-title">学费（元/年）</view>
          <view class="section-content">
            <view class="content-item">
              <view class="dot"></view>
              <text class="content-text">{{
                majorIntroduce.amount
              }}元/年</text>
            </view>
          </view>
        </view>
        <!-- 底部留白 -->
        <view class="bottom-space"></view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from "vue";

import { useSafeStorage } from "@/hooks/useSafeStorage";

const majorIntroduce = useSafeStorage("majorIntroduce", {});

// 响应式数据
const statusBarHeight = ref(0);

// 生命周期
onMounted(() => {
  // 获取状态栏高度
  // const systemInfo = uni.getSystemInfoSync();
  // statusBarHeight.value = systemInfo.statusBarHeight || 44;
});
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f6f6f6;
  display: flex;
  flex-direction: column;
}

/* 滚动内容区域 */
.content-scroll {
  flex: 1;
}

.content-container {
  padding: 40rpx 30rpx;
}

/* 信息区块 */
.info-section {
  margin-bottom: 48rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
  line-height: 44rpx;
}

.section-content {
  background-color: transparent;
}

.content-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background-color: #00c853;
  border-radius: 50%;
  margin-top: 14rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.content-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
  flex: 1;
}

.long-text {
  text-align: justify;
  word-break: break-all;
}



.bottom-space {
  height: 60rpx;
}

/* 响应式适配 */
/* @media (max-width: 480px) {
  .content-container {
    padding: 30rpx 24rpx;
  }

  .section-title {
    font-size: 30rpx;
  }

  .content-text {
    font-size: 26rpx;
    line-height: 38rpx;
  }
} */

/* 滚动优化 */
.content-scroll {
  scroll-behavior: smooth;
}

/* 长文本优化 */
.long-text {
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
}
</style>
