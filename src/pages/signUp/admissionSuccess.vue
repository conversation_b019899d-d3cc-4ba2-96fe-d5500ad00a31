<template>
  <view class="page-container">
    <image class="successImg" src="@/static/image/success1.png"></image>
    <view class="name">{{ state.name }}</view>
    <view class="schoolName">{{ state.schoolName }}</view>
  </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app";

const state = reactive({
  name: "",
  schoolName: "",
  reportStartDate: "",
});

onLoad((options) => {
  // 解析对象类型参数
  if (options.data) {
    // 直接从url上拿参数回来就行了 简单的死的参数
    const dataObj = JSON.parse(decodeURIComponent(options.data));
    console.log(dataObj);
    state.name = dataObj.name;
    state.schoolName = dataObj.schoolName;
    state.reportStartDate = dataObj.reportStartDate;
  }
});
</script>

<style scoped>
.page-container {
  position: relative;
  height: 100vh;
  background: #f6f6f6;
  padding: 24rpx 18rpx;
}
.successImg {
  width: 100%;
}

.name {
  position: absolute;
  top: 215rpx;
  left: 100rpx;
  font-size: 16rpx;
  color: #333333;
  font-weight: 400;
}

.schoolName {
  position: absolute;
  top: 275rpx;
  left: 215rpx;
  font-size: 16rpx;
  color: #333333;
  font-weight: 400;
}
</style>
