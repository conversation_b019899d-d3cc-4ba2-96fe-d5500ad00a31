<template>
  <view class="payment-container">
    <!-- 支付卡片 -->
    <view class="payment-card">
      <!-- 倒计时区域 -->
      <view class="countdown-section">
        <text class="countdown-text">支付剩余时间</text>
        <uni-countdown
          :show-day="false"
          :showHour="false"
          :minute="state.minute"
          :second="state.second"
          @timeup="timeup"
        />
      </view>
      <!-- 支付金额区域 -->
      <view class="amount-section">
        <text class="amount-symbol">¥</text>
        <text class="amount-value">{{ smsCheckPrepay.payAmountSum || 0 }}</text>
      </view>
      <!-- 订单信息区域 -->
      <view class="order-section">
        <view class="order-row">
          <text class="order-label">订单信息：</text>
          <text class="order-value">{{ orderInfo }}</text>
        </view>
      </view>
      <!-- 支付方式区域 -->
      <view v-if="payMethodList && payMethodList.length > 0">
        <view
          class="payment-method-section"
          v-for="(item, index) in payMethodList"
        >
          <view class="payment-method" @click="selectPaymentMethod(item)">
            <view class="method-left">
              <image
                class="wechat-icon"
                src="@/static/image/weixin.png"
                mode="aspectFit"
              ></image>
              <text class="method-text">{{ item.payMethodName }}</text>
            </view>
            <view class="method-right">
              <view class="check-icon" :class="{ checked: isWechatSelected }">
                <text class="check-text" v-if="isWechatSelected">✓</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view v-else class="no-payment-method">暂未配置收款信息，请联系老师</view>
    </view>

    <!-- 确认支付按钮 -->
    <view
      class="payment-button-section"
      v-if="payMethodList && payMethodList.length > 0"
    >
      <button
        class="confirm-payment-btn"
        :class="{ disabled: !canPay }"
        @click="publicPayment"
      >
        确认支付
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import dayjs from "dayjs";
import http from "@/utils/request";
import { onHide, onShow } from "@dcloudio/uni-app";
import usePayStore from "@/store/pay";

import { getOpenId } from "@/utils/auth";

import wx from "weixin-js-sdk";
// 使用 Pinia store 管理表单数据
const payStore = usePayStore();

// 从 Pinia store 获取数据
const smsCheckPrepay = computed(() => payStore.getSmsCheckPrepay);

let payMethodList = ref([]);

const state = reactive({
  minute: 0,
  second: 0,
});

// 支付相关数据
const paymentAmount = ref("1200.00");
const orderInfo = ref("xxxxxxxx订单");
const isWechatSelected = ref(false);

// 支付状态
const paymentStatus = ref("pending"); // pending, processing, success, failed, timeout

// 计算属性
const canPay = computed(() => {
  return isWechatSelected.value && paymentStatus.value !== "processing";
});

// 选择支付方式
const selectPaymentMethod = () => {
  isWechatSelected.value = !isWechatSelected.value;
  // 这里可以扩展其他支付方式
  if (isWechatSelected.value) {
    uni.showToast({
      title: "已选择微信支付",
      icon: "none",
      duration: 1000,
    });
  }
};

// 处理支付
const handlePayment = async () => {
  if (!canPay.value) {
    if (countdown.value <= 0) {
      uni.showToast({
        title: "支付已超时",
        icon: "none",
      });
    } else if (!isWechatSelected.value) {
      uni.showToast({
        title: "请选择支付方式",
        icon: "none",
      });
    }
    return;
  }

  try {
    paymentStatus.value = "processing";

    // 显示支付加载状态
    uni.showLoading({
      title: "正在调起支付...",
    });

    // 调用微信支付
    await initiateWechatPayment();
  } catch (error) {
    paymentStatus.value = "failed";
    uni.hideLoading();

    console.error("支付失败:", error);

    uni.showModal({
      title: "支付失败",
      content: error.message || "支付过程中出现错误，请重试",
      showCancel: true,
      cancelText: "取消",
      confirmText: "重试",
      success: (res) => {
        if (res.confirm) {
          paymentStatus.value = "pending";
        }
      },
    });
  }
};

// 发起微信支付
const initiateWechatPayment = async () => {
  try {
    // 第一步：调用微信支付
    const paymentResult = await requestWechatPayment(orderData);

    // // 第二步：处理支付结果
    // await handlePaymentResult(paymentResult);
  } catch (error) {
    throw error;
  }
};

// 调用H5微信支付
const requestWechatPayment = (orderData) => {
  return new Promise((resolve, reject) => {
    // #ifdef H5
    // H5端处理（通常跳转到微信支付页面）
    if (orderData.mwebUrl) {
      // 跳转到微信H5支付页面
      window.location.href = orderData.mwebUrl;
      window.open(h5_url, "_blank");
    } else {
      reject(new Error("H5支付暂不支持"));
    }
    // #endif
  });
};

// // 处理支付结果
// const handlePaymentResult = async (paymentResult) => {
//   try {
//     uni.hideLoading();

//     if (paymentResult.status === "success") {
//       // 验证支付结果
//       const verifyResult = await verifyPaymentResult(paymentResult);

//       if (verifyResult.success) {
//         paymentStatus.value = "success";
//         stopCountdown();

//         // 支付成功
//         uni.showToast({
//           title: "支付成功",
//           icon: "success",
//           duration: 2000,
//         });

//         // 延迟跳转到成功页面
//         setTimeout(() => {
//           uni.redirectTo({
//             url: `/pages/payment-success/`,
//           });
//         }, 2000);
//       } else {
//         throw new Error("支付验证失败");
//       }
//     } else {
//       throw new Error("支付未完成");
//     }
//   } catch (error) {
//     paymentStatus.value = "failed";
//     throw error;
//   }
// };

// 获取商家配置的支付方式配置 一般来说就是微信支付  也没开别的什么支付 写死就完事了
const getPayMethodList = () => {
  const merchantId = smsCheckPrepay.value.merchantId;
  const schoolId = smsCheckPrepay.value.schoolId;
  http
    .post("/campuspay/mobile/general-pay-center/payMethodList", {
      merchantId: merchantId,
      schoolId: schoolId,
    })
    .then((res) => {
      payMethodList.value = res.data;
    });
};

// 倒计时时间到
const timeup = () => {
  paymentStatus.value = "timeout";
  uni.showModal({
    title: "提示",
    content: "支付超时，请重新验证支付!",
    showCancel: false,
    success: function (res) {
      if (res.confirm) {
        payStore.resetPaty();
        // 清除所有支付信息 回到 我要缴费页面 重新用手机获取验证码支付
        uni.redirectTo({
          url: `/pages/checkIn/startPay`,
        });
      }
    },
  });
};

function onBridgeReady(params) {
  WeixinJSBridge.invoke("getBrandWCPayRequest", params, function (res) {
    console.log(res, "支付结果111111");
    if (res.err_msg == "get_brand_wcpay_request:ok") {
      console.log("支付成功");
      setTimeout(() => {
        // location.href = VITE_BASE_URL;
      }, 1000);
    
    } else {
      console.log("支付失败11111111");
    }
  });
}

// H5支付
const publicPayment = () => {
  if (!canPay.value) {
    if (countdown.value <= 0) {
      uni.showToast({
        title: "支付已超时",
        icon: "none",
      });
    } else if (!isWechatSelected.value) {
      uni.showToast({
        title: "请选择支付方式",
        icon: "none",
      });
    }
    return;
  }

  const params = {
    tradeNo: smsCheckPrepay.value.tradeNo, // 交易单号
    payAmountSum: smsCheckPrepay.value.payAmountSum, // 缴费金额
    configTypeId: payMethodList.value[0].configTypeId || null, // 配置类型ID
    payMethodId: payMethodList.value[0].payMethodId || null, // 支付方式
    schoolId: smsCheckPrepay.value.schoolId, // 学校ID
    payType: 1, 
    paySource: "公众号",
    openid: getOpenId(),
  };

  http
    .post("/campuspay/mobile/general-pay-center/paySubmit", params)
    .then((res) => {
      console.log(res.data.resultInfo.jsApiResult, "支付参数");
      const obj = {
        ...res.data.resultInfo.jsApiResult,
        package: res.data.resultInfo.jsApiResult.packageStr,
      }
      onBridgeReady(obj);
    })
    .finally(() => {});
};

// 生命周期
onMounted(() => {
  getPayMethodList(); // 获取支付方式配置
});

onShow(() => {
  const payEndTime = smsCheckPrepay.value.payEndTime; // 获取订单的最后截止提交时间
  const dayJspayEndTime = dayjs(payEndTime);
  const date = dayjs().format("YYYY-MM-DD HH:mm:ss"); // 获取当前时间
  // 如果订单的最后可支付时间大于当前时间 则订单超时了
  if (dayJspayEndTime.isBefore(date)) {
    return;
  }
  const time = dayjs(dayJspayEndTime.diff(date)).format("mm ss").split(" ");
  state.minute = parseInt(time[0]);
  state.second = parseInt(time[1]);
});
</script>

<style scoped>
/* 页面容器 */
.payment-container {
  height: 100vh;
  background-color: #f5f5f5;

  padding: 80rpx 30rpx 160rpx 30rpx;
  display: flex;
  flex-direction: column;
}

/* 支付卡片 */
.payment-card {
  flex: 1;
}

/* 倒计时区域 */
.countdown-section {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin-bottom: 60rpx;
}

.countdown-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
}

/* 支付金额区域 */
.amount-section {
  text-align: center;
  margin-bottom: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.amount-symbol {
  font-size: 48rpx;
  color: #333333;
  font-weight: 400;
  margin-right: 8rpx;
  margin-top: -10rpx;
}

.amount-value {
  font-size: 96rpx;
  color: #333333;

  line-height: 1;

  font-weight: 600;

  color: #333333;
}

/* 订单信息区域 */
.order-section {
  margin-bottom: 24rpx;
  padding: 38rpx 24rpx;

  background: #ffffff;
  border-radius: 12rpx;
}

.order-row {
  display: flex;
  align-items: flex-start;
}

.order-label {
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.order-value {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
  flex: 1;
}

/* 支付方式区域 */
.payment-method-section {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 38rpx 24rpx;
}

.payment-method {
  display: flex;
  align-items: center;
  justify-content: space-between;

  transition: background-color 0.3s ease;
}

.payment-method:active {
  background-color: #f8f9fa;
}

.method-left {
  display: flex;
  align-items: center;
}

.wechat-icon {
  width: 80rpx;
  height: 64rpx;
  margin-right: 24rpx;
}

.method-text {
  font-size: 32rpx;
  color: #333333;
  line-height: 44rpx;
}

.method-right {
  display: flex;
  align-items: center;
}

.check-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.check-icon.checked {
  background-color: #00d190;
  border-color: #00d190;
}

.check-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
}

/* 确认支付按钮区域 */
.payment-button-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;

  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}

.confirm-payment-btn {
  width: 100%;
  height: 80rpx;
  background-color: #00b781;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
  transition: all 0.3s ease;
}

.confirm-payment-btn.disabled {
  background-color: #cccccc;
  color: #999999;
}

/* 动画效果 */
.payment-card {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 支付处理中的加载状态 */
.confirm-payment-btn.processing {
  background-color: #999999;
  pointer-events: none;
}

.no-payment-method {
  font-weight: 600;
  font-size: 28rpx;
  color: #00b781;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
</style>
