<template>
  <view class="page-container">
    <view class="segmentation"> </view>
    <view class="form-container">
      <!-- 表单内容 -->
      <view class="form-content">
        <view class="form-item">
          <view class="label">
            <text class="label-text">姓名：</text>
          </view>
          <input
            disabled
            class="form-input"
            v-model="personalInfo.name"
            placeholder="请输入姓名"
          />
        </view>

        <view class="form-item">
          <view class="label">
            <text class="label-text">性别：</text>
          </view>
          <input
            disabled
            class="form-input"
            v-model="genderText"
            placeholder="请输入性别"
          />
        </view>

        <view class="form-item">
          <view class="label">
            <text class="label-text">手机号码</text>
          </view>
          <input
            disabled
            class="form-input"
            v-model="personalInfo.phone"
            placeholder="请输入手机号码"
            type="number"
          />
        </view>

        <view class="form-item">
          <view class="label">
            <text class="label-text">专业：</text>
          </view>
          <input
            disabled
            class="form-input"
            v-model="checkInMajorIntroduce.majorName"
            placeholder="请输入专业："
          />
        </view>
      </view>
    </view>

    <!-- 固定底部按钮 -->
    <view class="bottom-fixed">
      <button class="submit-btn" @click="handleSubmit">立即报到</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from "vue";
import http from "@/utils/request";
import { useSafeStorage } from "@/hooks/useSafeStorage";
import useCheckInStore from "@/store/checkIn";

// 使用 Pinia store 获取表单数据
const checkInStore = useCheckInStore();

// 获取专业信息（这个仍然使用原来的缓存方式）
const checkInMajorIntroduce = useSafeStorage("checkIn-selectSpecialty", {});

// 从 Pinia store 获取数据
const personalInfo = computed(() => checkInStore.getPersonalInfo);

const genderText = computed(() => {
  return personalInfo.value.gender === 1 ? "男" : "女";
});

// familyList这个参数需要过滤掉 名字关系电话都为空字符串的数据
const familyList = computed(() =>
  checkInStore.getFamilyInfo.filter((item) => {
    return item.name !== "" && item.relations !== "" && item.phone !== "";
  })
);
const allImgUrl = computed(() => checkInStore.getUploadedAttachments);

console.log("个人信息:", personalInfo.value);
console.log("家庭信息:", familyList.value);
console.log("上传附件:", allImgUrl.value);

// 页面加载时，清理旧的缓存数据（如果存在）
onMounted(() => {
  try {
    // 清理旧的缓存数据
    uni.removeStorageSync("checkIn-personalInfo");
    uni.removeStorageSync("checkIn-familyInfo");
    uni.removeStorageSync("checkIn-imgUrl");
  } catch (error) {
    console.log("清理旧缓存失败:", error);
  }
});

const handleSubmit = async () => {
  try {
    uni.showLoading({
      title: "提交中...",
    });

    // 从 Pinia store 获取上传附件数据（已经是正确的格式）
    const imgResult = allImgUrl.value;

    console.log("提交数据:", {
      个人信息: personalInfo.value,
      家庭信息: familyList.value,
      上传附件: imgResult,
      专业信息: checkInMajorIntroduce.value,
    });

    // API请求
    const res = await http.post("/app/enrollment/report/createReport", {
      schoolId: checkInMajorIntroduce.value.schoolId,
      planId: checkInMajorIntroduce.value.planId,
      enrollmentMajorId: checkInMajorIntroduce.value.id,
      ...personalInfo.value,
      isDormitory: personalInfo.value.isDormitory ? true : false,
      ...imgResult,
      familyList: familyList.value,
    });

    uni.hideLoading();

    // 提交成功后清空表单数据
    checkInStore.clearAllData();

    // 提交成功
    uni.showToast({
      title: "提交成功",
      icon: "success",
      duration: 2000,
    });

    // 成功
    uni.navigateTo({
      url: `/pages/checkIn/checkInSuccess?reportId=${res.data}`,
    });
  } catch (error) {
    uni.hideLoading();
    console.error("提交失败:", error);

    // 显示错误提示
    uni.showToast({
      title: "提交失败，请重试",
      icon: "error",
      duration: 2000,
    });
  }
};
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.segmentation {
  height: 20rpx;
  background-color: #f6f6f6;
}

.form-container {
  /* margin: 30rpx; */
  background-color: #ffffff;
  /* border-radius: 20rpx; */
  overflow: hidden;
  /* box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06); */
}

.form-title {
  background-color: #f6f6f6;

  padding: 40rpx 0 20rpx;
  border-bottom: 1rpx solid #f0f0f0;

  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
}

.form-content {
  padding: 0 40rpx 40rpx;
}

.form-item {
  display: flex;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 160rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.label.required {
  position: relative;
}

.required-star {
  color: #ff4757;
  font-size: 28rpx;
  margin-right: 4rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
}

.form-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333333;
  padding: 0 20rpx;
  background-color: transparent;
}

.form-input::placeholder {
  color: #c0c0c0;
}

/* 固定底部按钮 */
.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #00c853 0%, #4caf50 100%);
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* 响应式适配 */
/* @media (max-width: 480px) {
  .form-container {
    margin: 20rpx;
    border-radius: 16rpx;
  }

  .form-content {
    padding: 0 30rpx 30rpx;
  }

  .label {
    width: 140rpx;
  }

  .form-title,
  .label-text,
  .form-input {
    font-size: 26rpx;
  }
} */

.noBorder {
  :deep(.uni-select) {
    border: none !important;
  }
}
</style>
