<template>
  <view class="page-container">
    <view class="form-container">
      <!-- 页面标题 -->
      <view class="page-title">请输入手机号查询</view>

      <!-- 表单内容 -->
      <view class="form-content">
        <!-- 手机号 -->
        <view class="form-item">
          <view class="label required">
            <text class="required-star">*</text>
            <text class="label-text">手机号</text>
          </view>
          <input
            class="form-input"
            v-model="formData.phone"
            placeholder="请输入手机号"
            type="number"
            maxlength="11"
            @input="onPhoneInput"
            @blur="validatePhone"
          />
        </view>

        <!-- 验证码 -->
        <view class="form-item">
          <view class="label required">
            <text class="required-star">*</text>
            <text class="label-text">手机验证码</text>
          </view>
          <input
            class="form-input"
            v-model="formData.verifyCode"
            placeholder="请输入手机验证码"
            type="number"
            maxlength="6"
            @blur="validateVerifyCode"
          />
          <button
                class="verify-btn"
            :class="{ active: canGetCode }"
            @click="getVerifyCode"
            :disabled="!canGetCode || isCountingDown"
          >
            {{ btnText }}
          </button>
        </view>
      </view>
    </view>

    <!-- 固定底部按钮 -->
    <view class="bottom-fixed">
      <button
        class="query-btn"
        :class="{ disabled: !canSubmit }"
        @click="handleQuery"
      >
        立即缴费
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, reactive } from "vue";

import http from "@/utils/request";
import { getOpenId } from "@/utils/auth";

import usePayStore from "@/store/pay";
// 使用 Pinia store 管理表单数据
const payStore = usePayStore();

// 表单数据
const formData = reactive({
  phone: "",
  verifyCode: "",
});

// 验证码相关状态
const isCountingDown = ref(false);
const countDown = ref(60);
const timer = ref(null);

// 表单验证错误
const formErrors = reactive({
  phone: "",
  verifyCode: "",
});

// 计算属性
const canGetCode = computed(() => {
  const phoneReg = /^1[3-9]\d{9}$/;
  return phoneReg.test(formData.phone) && !formErrors.phone;
});

const canSubmit = computed(() => {
  return (
    formData.phone.trim() !== "" &&
    formData.verifyCode.trim() !== "" &&
    !formErrors.phone &&
    !formErrors.verifyCode
  );
});

const btnText = computed(() => {
  if (isCountingDown.value) {
    return `${countDown.value}s`;
  }
  return "获取验证码";
});

// 手机号输入处理
const onPhoneInput = () => {
  // 清除非数字字符
  formData.phone = formData.phone.replace(/\D/g, "");
  // 限制长度
  if (formData.phone.length > 11) {
    formData.phone = formData.phone.slice(0, 11);
  }
  // 清除错误信息
  if (formErrors.phone) {
    formErrors.phone = "";
  }
};

// 验证手机号
const validatePhone = () => {
  const phoneReg = /^1[3-9]\d{9}$/;

  if (!formData.phone.trim()) {
    formErrors.phone = "请输入手机号";
    showToast("请输入手机号");
    return false;
  }

  if (!phoneReg.test(formData.phone)) {
    formErrors.phone = "请输入正确的手机号格式";
    showToast("请输入正确的手机号格式");
    return false;
  }

  formErrors.phone = "";
  return true;
};

// 验证验证码
const validateVerifyCode = () => {
  if (!formData.verifyCode.trim()) {
    formErrors.verifyCode = "请输入验证码";
    showToast("请输入验证码");
    return false;
  }

  if (formData.verifyCode.length !== 6) {
    formErrors.verifyCode = "验证码应为6位数字";
    showToast("验证码应为6位数字");
    return false;
  }

  formErrors.verifyCode = "";
  return true;
};

// 获取验证码
const getVerifyCode = async () => {
  if (!validatePhone()) {
    return;
  }

  if (isCountingDown.value) {
    return;
  }

  try {
    uni.showLoading({
      title: "发送中...",
    });

    // 验证码API请求
    await sendVerifyCodeAPI(formData.phone);

    uni.hideLoading();

    // 开始倒计时
    startCountDown();

    uni.showToast({
      title: "验证码已发送",
      icon: "success",
      duration: 2000,
    });
  } catch (error) {
    uni.hideLoading();
    console.error("发送验证码失败:", error);

    uni.showToast({
      title: error.message || "发送失败，请重试",
      icon: "none",
      duration: 2000,
    });
  }
};

// 发送验证码API
const sendVerifyCodeAPI = (phone) => {
  return new Promise((resolve, reject) => {
    // 网络请求
    http
      .post("/app/sms/enrollment/message", {
        phone: phone,
      })
      .then(() => {
        resolve({
          code: 200,
          message: "发送成功",
        });
      })
      .catch(() => {
        reject(new Error("网络异常，请重试"));
      });
  });
};

// 开始倒计时
const startCountDown = () => {
  isCountingDown.value = true;
  countDown.value = 60;

  timer.value = setInterval(() => {
    countDown.value--;

    if (countDown.value <= 0) {
      stopCountDown();
    }
  }, 1000);
};

// 停止倒计时
const stopCountDown = () => {
  isCountingDown.value = false;
  countDown.value = 60;

  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
};

// 查询详情
const querycontent = (phone, code) => {
  return new Promise((resolve, reject) => {
    // 获取openid
    const openid = getOpenId();

    const requestData = {
      planId: uni.getStorageSync("planId") || null,
      phone: phone,
      smsCode: code,
    };

    // 如果有openid，添加到请求参数中
    if (openid) {
      requestData.openid = openid;
    }

    http
      .post("/app/enrollment/pay/smsCheckPrepay", requestData)
      .then((res) => {
        const content = res.data;
        payStore.setSmsCheckPrepay(content);
        resolve(res.data);
      })
      .catch((e) => {

        // 如果e.code === 1008009022 说明一个问题 他存在未支付的订单 这个时候直接去让他付钱就完事了
        if (e && e.code === 1008009022) {
          uni.navigateTo({
            url: `/pages/checkIn/payMoney`,
          });
        }
        reject(e);
      });
  });
};

// 查询录取状态
const handleQuery = async () => {
  // 验证表单
  const isPhoneValid = validatePhone();
  const isCodeValid = validateVerifyCode();

  if (!isPhoneValid || !isCodeValid) {
    return;
  }

  try {
    uni.showLoading({
      title: "加载中...",
    });

    // 查询缴费的前置条件
    const result = await querycontent(formData.phone, formData.verifyCode);
    console.log(result);

    uni.hideLoading();

    // 去缴费
    uni.navigateTo({
      url: `/pages/checkIn/payment`,
    });
  } catch (error) {
    uni.hideLoading();
  }
};

// 显示提示
const showToast = (message) => {
  uni.showToast({
    title: message,
    icon: "none",
    duration: 2000,
  });
};

// 页面卸载时清理定时器
import { onUnmounted } from "vue";

onUnmounted(() => {
  stopCountDown();
});
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f6f6f6;
  padding-bottom: 120rpx;
}

.form-container {
  background-color: #ffffff;

  overflow: hidden;
}

.page-title {
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
  padding: 24rpx 46rpx 24rpx 46rpx;
  background: #f6f6f6;
}

.form-content {
  padding: 0 40rpx 0 40rpx;
}

.form-item {
  display: flex;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 180rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.label.required {
  position: relative;
}

.required-star {
  color: #fd4f45;
  font-size: 24rpx;
  margin-right: 4rpx;
}

.label-text {
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
}

.form-input {
  flex: 1;
  height: 80rpx;
  font-size: 24rpx;
  color: #333333;
  padding: 0 20rpx;
  background-color: transparent;
}

.form-input .uni-input-placeholder {
  font-weight: 400;
  font-size: 24rpx;
  color: #bfbfbf;
}

.verify-btn {
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;

  border-radius: 30rpx;

  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff !important;

  background: #dadada !important;
}
.verify-btn.active {
  color: #ffffff !important;
  background: #00b781 !important;
}

.tips-container {
  padding: 30rpx 40rpx 40rpx;
  background-color: #fafafa;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-dot {
  color: #666666;
  font-size: 28rpx;
  margin-right: 12rpx;
  margin-top: 2rpx;
}

.tip-text {
  flex: 1;
  font-size: 24rpx;
  color: #666666;
  line-height: 36rpx;
}

.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #f6f6f6;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}

.query-btn {
  width: 100%;
  height: 80rpx;
  background: #00b781;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;

  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-weight: 400;
  font-size: 28rpx;
  color: #ffffff;
}

.query-btn.disabled {
  background: #cccccc;
  color: #999999;
}

/* 输入框聚焦动画 */
.form-input {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 倒计时数字动画 */
.verify-btn {
  transition: all 0.3s ease;
}
</style>
