<template>
  <view class="page-container">
    <view class="segmentation"> </view>
    <view class="form-container">
      <!-- 表单内容 -->
      <view class="form-content">
        <view class="form-item">
          <view class="label">
            <text class="label-text">姓名：</text>
          </view>
          <input
            class="form-input"
            v-model="personalInfo.name"
            placeholder="请输入姓名"
          />
        </view>

        <view class="form-item">
          <view class="label">
            <text class="label-text">性别：</text>
          </view>
          <input
            class="form-input"
            v-model="personalInfo.gender"
            placeholder="请输入性别"
          />
        </view>

        <view class="form-item">
          <view class="label">
            <text class="label-text">手机号码</text>
          </view>
          <input
            class="form-input"
            v-model="personalInfo.phone"
            placeholder="请输入手机号码"
            type="number"
          />
        </view>

        <view class="form-item">
          <view class="label">
            <text class="label-text">报到专业：</text>
          </view>
          <input
            class="form-input"
            v-model="personalInfo.enrollmentMajorName"
            placeholder="请输入专业："
          />
        </view>
      </view>
    </view>

    <!-- 固定底部按钮 -->
    <!-- <view class="bottom-fixed">
      <button class="submit-btn" @click="handleSubmit">立即报到</button>
    </view> -->
  </view>
</template>

<script setup>
import { ref, computed, reactive } from "vue";

import { useSafeStorage } from "@/hooks/useSafeStorage";

const personalInfo = useSafeStorage("checkIn-queryInfo", {});
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.segmentation {
  height: 20rpx;
  background-color: #f6f6f6;
}

.form-container {
  /* margin: 30rpx; */
  background-color: #ffffff;
  /* border-radius: 20rpx; */
  overflow: hidden;
  /* box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06); */
}

.form-title {
  background-color: #f6f6f6;

  padding: 40rpx 0 20rpx;
  border-bottom: 1rpx solid #f0f0f0;

  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
}

.form-content {
  padding: 0 40rpx 40rpx;
}

.form-item {
  display: flex;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 160rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.label.required {
  position: relative;
}

.required-star {
  color: #ff4757;
  font-size: 28rpx;
  margin-right: 4rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
}

.form-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333333;
  padding: 0 20rpx;
  background-color: transparent;
}

.form-input::placeholder {
  color: #c0c0c0;
}

/* 固定底部按钮 */
.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #00c853 0%, #4caf50 100%);
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* 响应式适配 */
/* @media (max-width: 480px) {
  .form-container {
    margin: 20rpx;
    border-radius: 16rpx;
  }

  .form-content {
    padding: 0 30rpx 30rpx;
  }

  .label {
    width: 140rpx;
  }

  .form-title,
  .label-text,
  .form-input {
    font-size: 26rpx;
  }
} */

.noBorder {
  :deep(.uni-select) {
    border: none !important;
  }
}
</style>
