<template>
  <view class="family-form">
    <view class="form-title">家庭成员</view>
    <!-- 表单容器 -->
    <uni-forms ref="formRef" :model="formData">
      <!-- 家庭成员循环渲染 -->
      <view
        v-for="(member, index) in formData.members"
        :key="index"
        class="member-section"
      >
        <!-- 成员标题 -->
        <view class="member-title">
          <view class="member-title_line"></view>

          <view class="member-title_text"> 成员{{ index + 1 }}</view>
        </view>

        <!-- 姓名字段 -->
        <uni-forms-item label="姓名" :name="['members', index, 'name']">
          <uni-easyinput v-model="member.name" placeholder="请输入姓名" />
        </uni-forms-item>

        <!-- 家庭关系字段 -->
        <uni-forms-item
          label="家庭关系"
          :name="['members', index, 'relations']"
        >
          <uni-data-select
            v-model="member.relations"
            :localdata="relationshipOptions"
            placeholder="请选择"
          />
        </uni-forms-item>

        <!-- 手机号字段 -->
        <uni-forms-item label="手机号" :name="['members', index, 'phone']">
          <uni-easyinput v-model="member.phone" placeholder="请输入手机号" />
        </uni-forms-item>

        <!-- 删除成员按钮 -->
        <button
          v-if="formData.members.length > 1"
          @click="removeMember(index)"
          class="remove-btn"
        >
          删除
        </button>
      </view>
    </uni-forms>

    <!-- 添加家庭成员按钮 -->
    <button @click="addMember" class="add-btn">
      <uni-icons
        style="padding-right: 4px; color: #00b781"
        type="plus-filled"
        size="18"
      ></uni-icons>
      添加家庭成员
    </button>

    <!-- 底部操作按钮 -->
    <view class="action-buttons">
      <button @click="handleNext" type="primary">下一步</button>
      <text class="skip-text" @click="handleSkip">跳过</text>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import useCheckInStore from "@/store/checkIn";

import { relationshipOptions } from "@/utils/china-area-data";

const formRef = ref(null);

// 使用 Pinia store 管理家庭成员数据
const checkInStore = useCheckInStore();

// 表单数据结构 - 直接使用 Pinia store 中的数据
const formData = reactive({
  members: checkInStore.familyInfo,
});

/**
 * 添加新的家庭成员
 */
const addMember = () => {
  checkInStore.addFamilyMember();
  // 数据会通过 Pinia 自动持久化
};

/**
 * 删除指定的家庭成员
 * @param {number} index - 要删除的成员索引
 */
const removeMember = (index) => {
  checkInStore.removeFamilyMember(index);
  // 数据会通过 Pinia 自动持久化
};

/**
 * 处理下一步操作
 */
const handleNext = async () => {
  try {
    // 数据已经通过 Pinia 自动保存，无需手动缓存
    console.log("家庭成员信息已保存:", formData.members);

    uni.navigateTo({
      url: "/pages/checkIn/uploadAttachment",
    });
  } catch (error) {
    console.error("跳转失败:", error);
  }
};

const handleSkip = () => {
  uni.showModal({
    title: "提示",
    content: "确定要跳过填写家庭信息吗？",
    success: (res) => {
      if (res.confirm) {
        uni.navigateTo({
          url: "/pages/checkIn/uploadAttachment",
        });
      }
    },
  });
};
</script>

<style scoped>
/* 页面主容器 */
.family-form {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 160rpx; /* 为固定底部按钮预留空间 */
}

.form-title {
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
  padding: 24rpx;
}

/* 家庭成员卡片样式 */
.member-section {
  margin-bottom: 40rpx;
  padding: 30rpx;
  background-color: #ffffff;
}

/* 成员标题样式 */
.member-title {
  display: flex;
  align-items: center;

  margin-bottom: 30rpx;
}

.member-title_text {
  font-size: 24rpx;
  padding-left: 6rpx;
  font-weight: 600;
  color: #333333;
}
.member-title_line {
  width: 6rpx;
  height: 20rpx;
  background: #00b781;
  border-radius: 4rpx;
}
/* 表单项样式深度选择器 */
:deep(.uni-forms-item) {
  margin-bottom: 0;
  padding-top: 10rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f7f7f7;
}

:deep(.uni-forms-item__label) {
  color: #333333;

  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
}

/* 输入框样式 */
:deep(.uni-easyinput__content),
:deep(.uni-data-select) {
  height: 80rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 2rpx solid #e5e5e5;
}

:deep(.uni-easyinput__content-input) {
  font-size: 28rpx;
  color: #333333;
}

:deep(.input-placeholder) {
  font-size: 28rpx;
  color: #999999;
}

/* 添加成员按钮 */
.add-btn {
  margin: 40rpx 0;
  width: 100%;
  height: 82rpx;
  line-height: 82rpx;
  text-align: center;
  font-size: 24rpx;
  color: #00b781;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 删除成员按钮 */
.remove-btn {
  margin-top: 24rpx;
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  color: #ff5a5f;
  background-color: #fff2f2;
  border: 2rpx solid #ffdbdc;
  border-radius: 12rpx;
}

/* 底部操作按钮容器 */
.action-buttons {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx 30rpx;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 操作按钮样式 */
.action-buttons button {
  width: 100%;
  height: 80rpx;
  line-height: 88rpx;
  font-size: 28rpx;
  border-radius: 39rpx;
}

/* 主要按钮（下一步） */
.action-buttons button[type="primary"] {
  background-color: #00b781;
  color: #ffffff;
}

.skip-text {
  padding-top: 28rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #8c8c8c;
}

/* 错误信息样式 */
:deep(.uni-forms-item__error) {
  color: #ff5a5f;
  font-size: 24rpx;
  padding-top: 8rpx;
}

/* 下拉选择器样式 */
:deep(.uni-data-select__selector) {
  border-radius: 12rpx;
}

:deep(.uni-data-select__selector-item) {
  font-size: 28rpx;
  padding: 20rpx;
}
</style>
