<template>
  <view class="page-container">
    <!-- 滚动容器 -->
    <scroll-view
      scroll-y="true"
      class="scroll-container"
      :show-scrollbar="false"
      enhanced
    >
      <view class="major-list">
        <view
          class="major-card"
          :class="{ selected: item.selected, pressing: item.pressing }"
          v-for="(item, index) in majorList"
          :key="item.id"
          @click="lookMajor(item)"
          @longpress="selectMajor(index)"
          @touchstart="onTouchStart(index)"
          @touchend="onTouchEnd(index)"
          @touchcancel="onTouchEnd(index)"
        >
          <!-- 选中状态指示器 -->

          <view class="select-indicator" v-if="item.selected">
            <text class="select-icon">✓</text>
          </view>

          <!-- 专业名称 -->
          <view class="major-name">{{ item.majorName }}</view>

          <!-- 专业信息 -->
          <view class="major-info"> 简介：{{ item.majorIntroduction }} </view>
        </view>
      </view>

      <!-- 底部留白，避免被固定按钮遮挡 -->
      <view class="bottom-space"></view>
    </scroll-view>

    <!-- 固定底部按钮 -->
    <view class="bottom-fixed">
      <button
        class="next-btn"
        :class="{ disabled: !hasSelectedMajor }"
        @click="goNext"
      >
        下一步
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";

import { useSafeStorage } from "@/hooks/useSafeStorage";

const infoConfig = useSafeStorage("checkIn-info", {});

const majorList = ref(infoConfig.value?.majorList || []);

const hasSelectedMajor = computed(() =>
  majorList.value.some((item) => item.selected)
);

const selectedMajors = computed(() =>
  majorList.value.find((item) => item.selected)
);

const selectMajor = (index) => {
  uni.vibrateShort(); // 短震动

  majorList.value[index].selected = !majorList.value[index].selected;
  // 只能单选一个专业
  majorList.value.forEach((item, i) => {
    if (i !== index) {
      item.selected = false;
    }
  });

  console.log(majorList.value[index].selected, "majorList");

  const action = majorList.value[index].selected ? "选中" : "取消选中";
  uni.showToast({
    title: `${action}：${majorList.value[index].majorName}`,
    icon: "none",
    duration: 1500,
  });
};

const lookMajor = (item) => {
  uni.setStorageSync("checkIn-majorIntroduce", JSON.stringify(item));
  uni.navigateTo({
    url: "/pages/checkIn/majorIntroduce",
  });
};

const onTouchStart = (index) => {
  majorList.value[index].pressing = true;
};

const onTouchEnd = (index) => {
  majorList.value[index].pressing = false;
};

const goNext = () => {
  if (!hasSelectedMajor.value) {
    uni.showToast({
      title: "请先长按选择专业",
      icon: "none",
    });
    return;
  }
  uni.setStorageSync(
    "checkIn-selectSpecialty",
    JSON.stringify(selectedMajors.value)
  );
  uni.navigateTo({
    url: "/pages/checkIn/personalDetails",
  });
};

onLoad(() => {
  setTimeout(() => {
    uni.showToast({
      title: "长按卡片可选择专业",
      icon: "none",
      duration: 2000,
    });
  }, 1000);
});
</script>

<style scoped>
.page-container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding: 24rpx;
}

/* 滚动容器 */
.scroll-container {
  flex: 1;
  /* padding: 24rpx; */
}

/* 专业列表 - Grid布局 */
.major-list {
  display: grid;
  grid-template-columns: 1fr 1fr; /* 两列等宽 */
  gap: 24rpx; /* 行列间距都是24rpx */
}

/* 专业卡片 */
.major-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  position: relative;
  transition: all 0.2s ease;
  border: 2rpx solid transparent;
  user-select: none; /* 防止文字选中 */
  overflow: hidden;
}

/* 按压状态 */
.major-card.pressing {
  transform: scale(0.95);
  /* background-color: #f8f8f8; */
}

/* 选中状态 */
.major-card.selected {
  border-color: #00b781;
}

/* 选中且按压状态 */
/* .major-card.selected.pressing {
  transform: scale(0.92);
  border-color: #00B781;
} */

/* 选中指示器 */
.select-indicator {
  position: absolute;
  top: -2rpx;
  right: -2rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #00b781;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: selectIndicatorShow 0.3s ease;
}

@keyframes selectIndicatorShow {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.select-icon {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: bold;
}

/* 专业名称 */
.major-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
  line-height: 44rpx;
  transition: color 0.2s ease;
  /* 专业名称单行显示 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 专业信息 */
.major-info {
  font-size: 24rpx;
  color: #666666;
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 显示省略号来代表被修剪的文本 */
}

/* 底部留白 */
.bottom-space {
  height: 120rpx;
}

/* 固定底部 */
.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #f6f6f6;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

/* 下一步按钮 */
.next-btn {
  width: 100%;
  height: 88rpx;
  background-color: #00b781;
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.next-btn.disabled {
  background-color: #cccccc;
  color: #999999;
}
</style>
